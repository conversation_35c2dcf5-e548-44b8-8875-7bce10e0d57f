body {
  font-family: Arial, sans-serif;
  background: #f4f4f4;
  margin: 0;
  padding: 0;
}
.container {
  max-width: 600px;
  margin: 40px auto;
  background: #fff;
  padding: 30px 40px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
h1 {
  text-align: center;
  color: #333;
}
form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}
input[type="file"] {
  padding: 8px;
}
button {
  background: #0078d7;
  color: #fff;
  border: none;
  padding: 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}
button:hover {
  background: #005fa3;
}
#result {
  margin-top: 24px;
}
#jsonOutput {
  background: #222;
  color: #0f0;
  padding: 16px;
  border-radius: 6px;
  font-size: 14px;
  overflow-x: auto;
  max-height: 400px;
}
