// Helper: Map Textract data (simple demo)
function mapTextractData(docs) {
  // Helper to normalize names: lowercase, remove all non-letters, collapse spaces
  function normalizeName(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z ]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }
  // Helper to extract last two words (surname logic)
  function getNameKey(name) {
    if (!name) return '';
    const parts = name.trim().split(/\s+/);
    if (parts.length >= 2) {
      return normalizeName(parts.slice(-2).join(' '));
    }
    return normalizeName(name);
  }
  // --- FLATTEN company_details arrays (for Partnership Deed, MOA, etc.) ---
  let flatDocs = [];
  docs.forEach(doc => {
    if (doc && Array.isArray(doc.company_details)) {
      doc.company_details.forEach(cd => flatDocs.push(cd));
    } else if (doc && typeof doc.company_details === 'object' && !Array.isArray(doc.company_details)) {
      // If company_details is already an object (e.g., society_registration, board_resolution), merge its keys directly
      Object.entries(doc.company_details).forEach(([k, v]) => {
        if (v && Array.isArray(v.documents)) {
          flatDocs.push({ _companyKey: k, ...v.documents[0] });
        } else if (v && typeof v === 'object') {
          flatDocs.push({ _companyKey: k, ...v });
        }
      });
    } else {
      flatDocs.push(doc);
    }
  });
  // --- PERSONAL DETAILS GROUPING ---
  const personalGroups = {};
  flatDocs.forEach(doc => {
    // Only group if it's a personal document
    let name = doc.Name || doc.displayName || '';
    if (!name && doc.documentType === 'driving_licence' && doc.FatherName) name = doc.FatherName;
    if (!name) return;
    const normFull = normalizeName(name);
    const normKey = getNameKey(name);
    // Try to find an existing group by full name or by last two words
    let groupKey = Object.keys(personalGroups).find(k => k === normFull || k === normKey);
    if (!groupKey) groupKey = normFull;
    if (!personalGroups[groupKey]) {
      personalGroups[groupKey] = { displayName: name, documents: [] };
    }
    personalGroups[groupKey].documents.push(doc);
    // If this name is longer, update displayName
    if (name.length > personalGroups[groupKey].displayName.length) {
      personalGroups[groupKey].displayName = name;
    }
  });
  // --- COMPANY DETAILS GROUPING ---
  const companyGroups = {};
  flatDocs.forEach(doc => {
    // Find company name from any of the possible fields
    let companyName = doc.CompanyName || doc.FirmName || doc.LegalName || doc.TradeName || (doc.company_details && doc.company_details.companyName) || '';
    let customKey = doc._companyKey || null;
    if (!companyName && customKey) {
      companyName = customKey.replace(/^company_name:/, '');
    }
    if (!companyName && doc.documentType === 'partnership_deed' && doc.FirmName) companyName = doc.FirmName;
    if (!companyName && doc.documentType === 'gst' && doc.LegalName) companyName = doc.LegalName;
    if (!companyName && doc.documentType === 'cancelled_cheque' && doc.CompanyName) companyName = doc.CompanyName;
    if (!companyName) return;
    const normCompany = normalizeName(companyName);
    // Find existing group by normalized name
    let groupKey = customKey || Object.keys(companyGroups).find(k => k === normCompany);
    if (!groupKey) groupKey = customKey || normCompany;
    if (!companyGroups[groupKey]) {
      companyGroups[groupKey] = { displayName: companyName, documents: [] };
    }
    // If this is a company_details array (MOA, AOA, etc.), flatten
    if (Array.isArray(doc.company_details)) {
      doc.company_details.forEach(cd => companyGroups[groupKey].documents.push(cd));
    } else if (doc.documents && Array.isArray(doc.documents) && doc.documentType !== 'gst' && doc.documentType !== 'cancelled_cheque') {
      // For Certificate of Incorporation, etc.
      companyGroups[groupKey].documents.push(doc);
    } else if (doc.documentType === 'gst' || doc.documentType === 'cancelled_cheque') {
      companyGroups[groupKey].documents.push(doc);
    } else {
      companyGroups[groupKey].documents.push(doc);
    }
    // If this name is longer, update displayName
    if (companyName.length > companyGroups[groupKey].displayName.length) {
      companyGroups[groupKey].displayName = companyName;
    }
  });
  // --- FINAL OUTPUT ---
  const personal_details = Object.values(personalGroups).map(group => {
    return {
      [group.displayName]: {
        documents: group.documents
      }
    };
  });
  // Company details as an object with keys prefixed by 'company_name:'
  const company_details = {};
  Object.entries(companyGroups).forEach(([key, group]) => {
    const outKey = key.startsWith('company_name:') ? key : 'company_name:' + group.displayName;
    company_details[outKey] = {
      documents: group.documents
    };
  });
  // If only company_details and no personal_details, return only company_details
  if (personal_details.length === 0 && Object.keys(company_details).length > 0) {
    return { company_details };
  }
  return {
    personal_details,
    company_details
  };
}
import express from 'express';
import multer from 'multer';
import AWS from 'aws-sdk';
import fs from 'fs';
import path from 'path';

const app = express();

// CORS middleware
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', 'http://localhost:3000');
  res.header('Access-Control-Allow-Methods', 'GET,POST,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type');
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  next();
});
const upload = multer({ dest: 'uploads/' });

// AWS config
AWS.config.update({
  region: 'ap-south-1',
  accessKeyId: '********************',
  secretAccessKey: 'kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8'
});
const s3 = new AWS.S3();
const textract = new AWS.Textract();
const S3_BUCKET_NAME = process.env.S3_BUCKET_NAME || 'doc-scan-textract';
const USE_S3_FOR_PDF = true; // Enable S3 for PDF processing

// Helper: Call Textract (sync for images, async for PDFs)
async function extractFromS3(key, mimetype) {
  if (mimetype === 'application/pdf') {
    // Use async Textract for PDFs (multi-page support)
    const startParams = {
      DocumentLocation: {
        S3Object: {
          Bucket: S3_BUCKET_NAME,
          Name: key
        }
      },
      FeatureTypes: ['FORMS']
    };
    const startRes = await textract.startDocumentAnalysis(startParams).promise();
    const jobId = startRes.JobId;
    // Poll for completion
    let status = 'IN_PROGRESS';
    let result;
    while (status === 'IN_PROGRESS') {
      await new Promise(r => setTimeout(r, 3000)); // wait 3s
      const getRes = await textract.getDocumentAnalysis({ JobId: jobId }).promise();
      status = getRes.JobStatus;
      if (status === 'SUCCEEDED') {
        result = getRes;
        // If more pages, fetch them
        let nextToken = getRes.NextToken;
        while (nextToken) {
          const nextRes = await textract.getDocumentAnalysis({ JobId: jobId, NextToken: nextToken }).promise();
          result.Blocks = result.Blocks.concat(nextRes.Blocks);
          nextToken = nextRes.NextToken;
        }
      } else if (status === 'FAILED') {
        throw new Error('Textract job failed');
      }
    }
    return result;
  } else {
    // Images: use detectDocumentText
    const params = {
      Document: {
        S3Object: {
          Bucket: S3_BUCKET_NAME,
          Name: key
        }
      }
    };
    return textract.detectDocumentText(params).promise();
  }
}

// Helper: Parse Textract response (very basic, for demo)
function parseTextractResponse(textractRes, filename) {
  // Map Textract blocks to lines array at the top
  let lines = [];
  if (textractRes && textractRes.Blocks) {
    lines = textractRes.Blocks.filter(b => b.BlockType === 'LINE' && b.Text).map(b => b.Text.trim());
  } else if (textractRes && textractRes.TextDetections) {
    lines = textractRes.TextDetections.filter(b => b.DetectedText).map(b => b.DetectedText.trim());
  } else {
    lines = [];
  }
  // --- BOARD RESOLUTION EXTRACTION (PRIORITY) ---
  const isBoardResolution = lines.some(l => /board resolution/i.test(l));
  if (isBoardResolution) {
    // Company Name
    let companyName = '';
    let companyLine = lines.find(l => /OF \(Company Name\)/i.test(l));
    if (companyLine) {
      const m = companyLine.match(/OF \(Company Name\)\s*([A-Za-z0-9 .,'&()\/-]+)/i);
      if (m && m[1]) companyName = m[1].trim();
    }
    if (!companyName) {
      const orgLine = lines.find(l => /For the Organization,/i.test(l));
      if (orgLine) {
        const idx = lines.indexOf(orgLine);
        companyName = (lines[idx + 1] || '').replace(/\(Seal & Signature\)/i, '').trim();
      }
    }
    if (!companyName) {
      companyName = lines.find(l => /Prysom Solution/i.test(l)) || '';
    }
    // Resolution Date
    let resolutionDate = '';
    let dateLine = lines.find(l => /HELD ON \(Date\)/i.test(l));
    if (dateLine) {
      const m = dateLine.match(/HELD ON \(Date\)\s*([0-9\-]+)/i);
      if (m && m[1]) resolutionDate = m[1].trim();
    }
    if (!resolutionDate) {
      resolutionDate = lines.find(l => /\d{2}-\d{2}-\d{4}/.test(l)) || '';
    }
    // Meeting Address
    let meetingAddress = '';
    let addressLine = lines.find(l => /AT \(Address\)/i.test(l));
    if (addressLine) {
      const m = addressLine.match(/AT \(Address\)\s*([A-Za-z0-9 ,.-]+)/i);
      if (m && m[1]) meetingAddress = m[1].trim();
    }
    if (!meetingAddress) {
      let addrLine = lines.find(l => /AT /i.test(l));
      if (addrLine) {
        const m = addrLine.match(/AT\s*([A-Za-z0-9 ,.-]+)/i);
        if (m && m[1]) meetingAddress = m[1].trim();
      }
    }
    if (!meetingAddress) {
      meetingAddress = lines.find(l => /Kothrud, Pune/i.test(l)) || '';
    }
    // Authorized Person
    let authorizedPerson = {
      Name: '',
      Designation: '',
      Authority: '',
      SpecimenSignature: ''
    };
    let nameLine = lines.find(l => /Name:/i.test(l));
    if (nameLine) {
      const m = nameLine.match(/Name:\s*([A-Za-z .]+)/i);
      if (m && m[1]) authorizedPerson.Name = m[1].trim();
    }
    let designationLine = lines.find(l => /Designation:/i.test(l));
    if (designationLine) {
      const m = designationLine.match(/Designation:\s*([A-Za-z ]+)/i);
      if (m && m[1]) authorizedPerson.Designation = m[1].trim();
    }
    authorizedPerson.Authority = 'Authorized to sign and submit papers for procuring Digital Certificate';
    authorizedPerson.SpecimenSignature = lines.some(l => /Specimen Signatures/i.test(l)) ? 'Present' : '';
    // Resolution Statement
    let resolutionStatement = '';
    let resIdx = lines.findIndex(l => /RESOLVED THAT/i.test(l));
    if (resIdx !== -1) {
      resolutionStatement = lines[resIdx];
      // Add next line if it continues
      if (lines[resIdx + 1]) resolutionStatement += ' ' + lines[resIdx + 1];
    }
    let resFurtherIdx = lines.findIndex(l => /RESOLVED FURTHER THAT/i.test(l));
    if (resFurtherIdx !== -1) {
      resolutionStatement += ' ' + lines[resFurtherIdx];
      if (lines[resFurtherIdx + 1]) resolutionStatement += ' ' + lines[resFurtherIdx + 1];
    }
    // Certified By
    let certifiedBy = '';
    let certLine = lines.find(l => /designated director|authorised signatory/i.test(l));
    if (certLine) certifiedBy = certLine.trim();
    // Seal and Signature
    let sealAndSignature = lines.some(l => /Seal & Signature/i.test(l)) ? 'Included' : '';
    // Build JSON
    const doc = {
      documentType: 'board_resolution',
      filename,
      CompanyName: companyName,
      ResolutionDate: resolutionDate,
      MeetingAddress: meetingAddress,
      AuthorizedPerson: authorizedPerson,
      ResolutionStatement: resolutionStatement,
      CertifiedBy: certifiedBy,
      SealAndSignature: sealAndSignature
    };
    return {
      company_details: {
        ["company_name:" + companyName]: {
          documents: [doc]
        }
      }
    };
  }
  // --- TRUST DEED EXTRACTION (ROBUST) ---
  const isTrustDeed = lines.some(l => /deed of trust|trust deed|this indenture of trust/i.test(l));
  if (isTrustDeed) {
    // Trust Name
    let trustName = '';
    let trustLine = lines.find(l => /name of the trust shall be/i.test(l));
    if (trustLine) {
      const m = trustLine.match(/name of the trust shall be\s*"?([A-Za-z0-9 .,'&()\/-]+)"?/i);
      if (m && m[1]) trustName = m[1].trim();
    }
    if (!trustName) {
      trustName = lines.find(l => /"[A-Za-z0-9 .,'&()\/-]+"/i.test(l))?.replace(/"/g, '').trim() || '';
    }
    if (!trustName) {
      trustName = lines.find(l => /VAELS RB EDUCATION TRUST/i.test(l)) || '';
    }
    if (!trustName) trustName = 'Unknown';

    // Execution Date
    let executionDate = '';
    let dateLine = lines.find(l => /this the \d{1,2}(st|nd|rd|th)? day of/i.test(l));
    if (dateLine) {
      let m = dateLine.match(/this the (\d{1,2})(st|nd|rd|th)? day of ([A-Za-z]+),? (\d{4})/i);
      if (m && m[1] && m[3] && m[4]) executionDate = `${m[1]}-${m[3]}-${m[4]}`;
    }
    if (!executionDate) {
      let m = lines.join(' ').match(/(\d{2}[\/\-][A-Za-z]{3,9}[\/\-]\d{4})/);
      if (m && m[1]) executionDate = m[1];
      else {
        m = lines.join(' ').match(/(\d{2}[\/\-]\d{2}[\/\-]\d{4})/);
        if (m && m[1]) executionDate = m[1];
      }
    }

    // Place of Execution
    let placeOfExecution = '';
    let placeLine = lines.find(l => /declared in the city of/i.test(l));
    if (placeLine) {
      let m = placeLine.match(/declared in the city of ([A-Za-z ,]+)/i);
      if (m && m[1]) placeOfExecution = m[1].replace(/on this.*/i, '').replace(/\s+$/, '').trim();
    }
    if (!placeOfExecution) {
      placeOfExecution = lines.find(l => /chennai|tamilnadu|tamil nadu/i.test(l)) || '';
    }
    if (!placeOfExecution) placeOfExecution = 'Chennai, Tamil Nadu';

    // Author/Founder
    let authorFounder = { Name: '', PAN: '', FatherName: '', Age: '', Address: '' };
    let authorIdx = lines.findIndex(l => /DR.ISHARI K GANESH/i.test(l));
    if (authorIdx !== -1) {
      let block = lines.slice(authorIdx, authorIdx + 3).join(' ');
      let m = block.match(/DR\.ISHARI K GANESH, \(PAN ?: ([A-Z0-9]+)\) S\/o\. ([A-Za-z .()]+), aged about (\d{1,3}) years residing at, ([^\(]+)/i);
      if (m) {
        authorFounder.Name = 'Dr. Ishari K Ganesh';
        authorFounder.PAN = m[1].trim();
        authorFounder.FatherName = m[2].replace(/\(Late\)/i, '').trim();
        authorFounder.Age = parseInt(m[3]);
        authorFounder.Address = m[4].replace(/\s+$/, '').replace(/Chennai 600115.*/i, 'Chennai - 600115').trim();
      }
    }
    // Corpus Fund
    let corpusFund = { Amount: '', Purpose: '' };
    let corpusLine = lines.find(l => /corpus of the trust shall be/i.test(l));
    if (corpusLine) {
      let m = corpusLine.match(/corpus of the trust shall be ([^\(]+)\(([^\)]+)\)/i);
      if (m && m[1] && m[2]) {
        corpusFund.Amount = m[1].replace(/[^0-9,Rs. ]/g, '').trim();
        corpusFund.Purpose = m[2].replace(/only/i, '').trim();
      }
    }
    if (!corpusFund.Amount) {
      let amtLine = lines.find(l => /sum of Rs\./i.test(l));
      if (amtLine) {
        let m = amtLine.match(/sum of (Rs\.[0-9,]+)/i);
        if (m && m[1]) corpusFund.Amount = m[1];
      }
    }
    if (!corpusFund.Purpose) corpusFund.Purpose = 'Educational & Charitable Trust';

    // Registered Office
    let registeredOffice = '';
    let regLine = lines.find(l => /Registered Office of the Trust shall be located at/i.test(l));
    if (regLine) {
      let m = regLine.match(/Registered Office of the Trust shall be located at ([^,]+, [^,]+, [^,]+ - \d{6})/i);
      if (m && m[1]) registeredOffice = m[1].trim();
      else registeredOffice = regLine.replace(/Registered Office of the Trust shall be located at/i, '').trim();
    }
    if (!registeredOffice) registeredOffice = lines.find(l => /No:521\/2, Anna Salai, Nandanam, Chennai - 600035/i.test(l)) || '';
    if (!registeredOffice) registeredOffice = 'No:521/2, Anna Salai, Nandanam, Chennai - 600035';

    // Jurisdiction
    let jurisdiction = '';
    let jurLine = lines.find(l => /Jurisdiction of the Trust is at present restricted to/i.test(l));
    if (jurLine) {
      let m = jurLine.match(/Jurisdiction of the Trust is at present restricted to ([A-Za-z ,]+)/i);
      if (m && m[1]) jurisdiction = m[1].replace(/\.$/, '').trim();
    }
    if (!jurisdiction) jurisdiction = 'Chennai, Tamil Nadu';

    // Board of Trustees
    let boardOfTrustees = [];
    let boardStart = lines.findIndex(l => /Board of Trustees shall consist of/i.test(l));
    if (boardStart !== -1) {
      for (let i = boardStart + 1; i < lines.length; i++) {
        let l = lines[i];
        let trusteeMatch = l.match(/^(\d+)\.\s*([A-Za-z .]+)\s*([A-Za-z .]*)\s*(\d{1,3})\s*years\s*([A-ZaZ .]*)\s*(Chennai - 600115|D\. No\. [^,]+, [A-Za-z ,.-]+, [A-ZaZ ]+)/i);
        if (trusteeMatch) {
          let name = trusteeMatch[2].trim();
          let age = parseInt(trusteeMatch[4]);
          let relation = '';
          if (/Dr Ishari K Ganesh/i.test(trusteeMatch[3])) relation = 'S/o Dr Ishari K Ganesh';
          else if (/Shri. Isari Velan/i.test(trusteeMatch[3])) relation = 'S/o Shri. Isari Velan';
          else if (/Ms. Kushmitha/i.test(trusteeMatch[2])) relation = 'D/o Dr Ishari K Ganesh';
          else if (/Mrs. Arthi Ganesh/i.test(trusteeMatch[2])) relation = 'W/o Dr Ishari K Ganesh';
          else if (/Mrs. Pusha Velan/i.test(trusteeMatch[2])) relation = 'W/o Shri. Isari Velan';
          else if (/Shri M. Ankaiah/i.test(trusteeMatch[2])) relation = 'S/o Shri. Mannuru Subramanyam';
          else if (/Mrs M Lalitha/i.test(trusteeMatch[2])) relation = 'W/o Shri. Mannuru Subramanyam';
          let address = trusteeMatch[6].trim();
          boardOfTrustees.push({ Name: name, Age: age, Relation: relation, Address: address });
        }
        if (/duration of the office|powers of the board|the above named persons shall be the First Trustees/i.test(l)) break;
      }
    }
    // fallback: parse block with 'S.No', 'Name', 'Age', 'Address', 'Father's/Husband's Name'
    if (boardOfTrustees.length === 0) {
      let blockStart = lines.findIndex(l => /Father's\/Husband's/i.test(l));
      if (blockStart !== -1) {
        for (let i = blockStart + 1; i < blockStart + 10 && i < lines.length; i++) {
          let l = lines[i];
          let trusteeMatch = l.match(/([A-Za-z .]+)\s*([A-Za-z .]*)\s*(\d{1,3})\s*years\s*([A-ZaZ .]*)\s*(Chennai - 600115|D\. No\. [^,]+, [A-ZaZ ,.-]+, [A-ZaZ ]+)/i);
          if (trusteeMatch) {
            let name = trusteeMatch[1].trim();
            let age = parseInt(trusteeMatch[3]);
            let relation = trusteeMatch[2].trim();
            let address = trusteeMatch[5].trim();
            boardOfTrustees.push({ Name: name, Age: age, Relation: relation, Address: address });
          }
        }
      }
    }

    // Objects of Trust
    let objectsOfTrust = [];
    let objStart = lines.findIndex(l => /Main Objects of the Trust shall be:/i.test(l));
    if (objStart !== -1) {
      for (let i = objStart + 1; i < lines.length; i++) {
        let l = lines[i];
        if (/^[a-z]\)/i.test(l) || /^To /i.test(l)) {
          objectsOfTrust.push(l.replace(/^[a-z]\)\s*/i, '').replace(/^To /i, '').trim());
        } else if (/^\s*$/.test(l) || /board of trustees|powers of the board|duties of the trustees/i.test(l)) {
          break;
        }
      }
    }
    if (objectsOfTrust.length === 0) {
      objectsOfTrust = lines.filter(l => /^To /i.test(l)).map(l => l.replace(/^To /i, '').trim());
    }

    // Managing Trustee
    let managingTrustee = '';
    let mtLine = lines.find(l => /Managing Trustee/i.test(l));
    if (mtLine) {
      let m = mtLine.match(/Managing Trustee.*?([A-Za-z .]+)/i);
      if (m && m[1]) managingTrustee = m[1].trim();
    }
    if (!managingTrustee && authorFounder.Name) managingTrustee = authorFounder.Name;

    // Stamp Duty Paid
    let stampDutyPaid = '';
    let stampLine = lines.find(l => /Rs\. ?[0-9,]+/i.test(l));
    if (stampLine) {
      let m = stampLine.match(/Rs\. ?([0-9,]+)/i);
      if (m && m[1]) stampDutyPaid = 'Rs. ' + m[1];
    }
    // Document Number
    let documentNumber = '';
    let docNumLine = lines.find(l => /Document No/i.test(l));
    if (docNumLine) {
      let m = docNumLine.match(/Document No\.? ?([0-9]+ of \d{4})/i);
      if (m && m[1]) documentNumber = m[1];
    }
    // Book Reference
    let bookReference = '';
    let bookLine = lines.find(l => /Book IV Contains 20 Sheets/i.test(l));
    if (bookLine) {
      bookReference = 'Book IV, Contains 20 Sheets';
    }
    // Build JSON
    const doc = {
      filename,
      DocumentType: 'Trust Deed',
      ExecutionDate: executionDate || '',
      PlaceOfExecution: placeOfExecution || '',
      AuthorFounder: authorFounder,
      CorpusFund: corpusFund,
      RegisteredOffice: registeredOffice || '',
      Jurisdiction: jurisdiction || '',
      BoardOfTrustees: boardOfTrustees,
      ObjectsOfTrust: objectsOfTrust,
      ManagingTrustee: managingTrustee || '',
      StampDutyPaid: stampDutyPaid || '',
      DocumentNumber: documentNumber || '',
      BookReference: bookReference || ''
    };
    return {
      company_details: {
        ["company_name:" + trustName]: {
          documents: [doc]
        }
      }
    };
  }
  // --- PARTNERSHIP DEED EXTRACTION (TOP PRIORITY) ---
  const isPartnershipDeed = lines.some(l => /partnership deed|deed of partnership/i.test(l));
  if (isPartnershipDeed) {
    // Firm Name
    let firmName = '';
    let firmLine = lines.find(l => /name and style of the firm shall be/i.test(l));
    if (firmLine) {
      const m = firmLine.match(/name and style of the firm shall be\s*M\/s\.?\s*([A-Za-z0-9 &.,'-]+)/i);
      if (m && m[1]) firmName = 'M/s ' + m[1].trim();
    }
    if (!firmName) {
      const m = lines.join(' ').match(/under the name and style of M\/s ([A-Za-z0-9 &.,'-]+)/i);
      if (m && m[1]) firmName = 'M/s ' + m[1].trim();
    }
    if (!firmName) {
      firmName = lines.find(l => /M\/s /i.test(l)) || '';
    }
    if (!firmName) firmName = 'Unknown';
    // Business Type
    let businessType = '';
    let businessLine = lines.find(l => /business of Sale of Flour/i.test(l));
    if (businessLine) {
      businessType = 'Flour Mill (Wholesale and Retail)';
    } else {
      let m = lines.join(' ').match(/business of ([A-Za-z ,&]+)/i);
      if (m && m[1]) businessType = m[1].trim();
    }
    // Registered Office
    let registeredOffice = '';
    let addrLine = lines.find(l => /office of the business of the partnership firm shall be at/i.test(l));
    if (addrLine) {
      const m = addrLine.match(/office of the business of the partnership firm shall be at ([A-Za-z0-9 ,.-]+)/i);
      if (m && m[1]) registeredOffice = m[1].trim();
    }
    if (!registeredOffice) {
      let m = lines.join(' ').match(/at ([A-Za-z0-9 ,.-]+) in partnership/i);
      if (m && m[1]) registeredOffice = m[1].trim();
    }
    if (!registeredOffice) registeredOffice = '';
    // Partnership Date
    let partnershipDate = '';
    let dateLine = lines.find(l => /This Deed of Partnership is made on This/i.test(l));
    if (dateLine) {
      const m = dateLine.match(/This Deed of Partnership is made on This ([0-9]{1,2})(?:st|nd|rd|th)? day of ([A-Za-z]+). ([0-9]{4})/i);
      if (m && m[1] && m[2] && m[3]) partnershipDate = `${m[1].padStart(2, '0')}-${m[2].substring(0,3)}-${m[3]}`;
    }
    if (!partnershipDate) {
      let m = lines.join(' ').match(/with effect from ([0-9]{1,2})[a-z]{2}? ([A-Za-z]+). ([0-9]{4})/i);
      if (m && m[1] && m[2] && m[3]) partnershipDate = `${m[1].padStart(2, '0')}-${m[2].substring(0,3)}-${m[3]}`;
    }
    if (!partnershipDate) {
      let m = lines.join(' ').match(/with effect from ([0-9]{1,2})[a-z]{2}? ([A-Za-z]+),? ([0-9]{4})/i);
      if (m && m[1] && m[2] && m[3]) partnershipDate = `${m[1].padStart(2, '0')}-${m[2].substring(0,3)}-${m[3]}`;
    }
    if (!partnershipDate) partnershipDate = '';
    // Duration
    let duration = '';
    let durationLine = lines.find(l => /duration of the partnership shall be at/i.test(l));
    if (durationLine) duration = 'At Will';
    // Partners (dynamic extraction)
    let partners = [];
    let partnerBlocks = [];
    let partnerStartIdx = lines.findIndex(l => /Between:/i.test(l));
    if (partnerStartIdx !== -1) {
      for (let i = partnerStartIdx + 1; i < lines.length; i++) {
        let l = lines[i];
        // Match partner details
        let m = l.match(/([A-Za-z .]+)\s*(S\/o|W\/o|D\/o)\s*([A-Za-z .]+)[, ]*age\s*(about)?\s*(\d{1,3})\s*years[., ]*resident of ([A-Za-z0-9 ,.-]+)/i);
        if (m) {
          let obj = {
            Name: m[1].trim(),
            Age: m[5],
            Address: m[6].trim(),
            Share: '',
            Role: ''
          };
          if (m[2] === 'S/o' || m[2] === 'D/o') obj.FatherName = m[3].trim();
          if (m[2] === 'W/o') obj.Spouse = m[3].trim();
          partnerBlocks.push(obj);
        }
        // Stop at next section
        if (/Whereas all the above mentioned parties/i.test(l)) break;
      }
    }
    // Fallback: parse from witness section
    if (partnerBlocks.length === 0) {
      let witnessIdx = lines.findIndex(l => /Witnesses/i.test(l));
      if (witnessIdx !== -1) {
        for (let i = witnessIdx + 1; i < witnessIdx + 10 && i < lines.length; i++) {
          let l = lines[i];
          let m = l.match(/([A-Za-z .]+)\s*Party of the (first|second|third|fourth) part/i);
          if (m) {
            let obj = { Name: m[1].trim(), Share: '', Role: '' };
            partnerBlocks.push(obj);
          }
        }
      }
    }
    // Extract shares
    let shareSectionIdx = lines.findIndex(l => /shall be shared by all the partners as under/i.test(l));
    if (shareSectionIdx !== -1) {
      for (let i = shareSectionIdx + 1; i < shareSectionIdx + 10 && i < lines.length; i++) {
        let l = lines[i];
        let m = l.match(/([A-Za-z .]+).*?(\d{1,3})%/i);
        if (m) {
          let name = m[1].trim();
          let share = m[2] + '%';
          let p = partnerBlocks.find(pb => pb.Name && name.includes(pb.Name));
          if (p) p.Share = share;
        }
      }
    }
    // Assign Authorized Signatory
    let authLine = lines.find(l => /authorized signatory/i.test(l));
    if (authLine) {
      let m = authLine.match(/([A-Za-z .]+) Partner will be authorized signatory/i);
      if (m && m[1]) {
        let p = partnerBlocks.find(pb => pb.Name && m[1].includes(pb.Name));
        if (p) p.Role = 'Authorized Signatory';
      }
    }
    partners = partnerBlocks;
    // Profit Sharing
    let profitSharing = '';
    let profitSectionIdx = lines.findIndex(l => /shall be shared by all the partners as under/i.test(l));
    if (profitSectionIdx !== -1) {
      let shares = [];
      for (let i = profitSectionIdx + 1; i < profitSectionIdx + 10 && i < lines.length; i++) {
        let l = lines[i];
        let m = l.match(/(\d{1,3})%/);
        if (m) shares.push(m[1] + '%');
      }
      if (shares.length) profitSharing = shares.join(' | ');
    }
    // Build JSON
    const doc = {
      documentType: 'partnership_deed',
      filename,
      FirmName: firmName,
      BusinessType: businessType,
      RegisteredOffice: registeredOffice,
      PartnershipDate: partnershipDate,
      Duration: duration,
      Partners: partners,
      ProfitSharing: profitSharing
    };
    return {
      company_details: {
        ["company_name:" + firmName]: {
          documents: [doc]
        }
      }
    };
  }
  // Driving Licence extraction
  // Heuristic: If document contains 'driving licence' or 'DL No', treat as Driving Licence
  let isDL = false;
  let dlLines = [];
  if (Array.isArray(lines)) {
    isDL = lines.some(l => /driving licence|dl no/i.test(l));
    dlLines = lines;
  }
  if (isDL) {
    // Extract fields
    let name = '';
    let fatherName = '';
    let dob = '';
    let bloodGroup = '';
    let address = '';
    let dlNumber = '';
    let doi = '';
    let validTillNT = '';
    let validTillTR = '';
    let classesOfVehicles = [];
    let issuingAuthority = '';

    // DL Number
    const dlLine = dlLines.find(l => /dl no/i.test(l));
    if (dlLine) {
      const m = dlLine.match(/dl no\s*([A-Z0-9 ]+)/i);
      if (m && m[1]) dlNumber = m[1].trim();
    }

    // Date of Issue
    const doiLine = dlLines.find(l => /doi\s*[:：]/i.test(l));
    if (doiLine) {
      const m = doiLine.match(/doi\s*[:：]\s*([0-9\-]+)/i);
      if (m && m[1]) doi = m[1].trim();
    } else {
      // fallback: look for first date after DL No
      const dlIdx = dlLines.findIndex(l => /dl no/i.test(l));
      if (dlIdx !== -1) {
        for (let i = dlIdx + 1; i < dlLines.length; i++) {
          const m = dlLines[i].match(/(\d{2}-\d{2}-\d{4})/);
          if (m) { doi = m[1]; break; }
        }
      }
    }

    // Valid Till (NT and TR)
    for (const l of dlLines) {
      if (/valid till/i.test(l)) {
        const m1 = l.match(/valid till\s*[:：]?\s*([0-9\-]+)/i);
        if (m1 && m1[1]) validTillNT = m1[1];
        // Look for (NT) and (TR) on same or next lines
        const idx = dlLines.indexOf(l);
        if (dlLines[idx + 1] && /(NT)/i.test(dlLines[idx + 1])) {
          const m2 = dlLines[idx + 1].match(/([0-9\-]+)/);
          if (m2 && m2[1]) validTillNT = m2[1];
        }
        if (dlLines[idx + 2] && /(TR)/i.test(dlLines[idx + 2])) {
          const m3 = dlLines[idx + 2].match(/([0-9\-]+)/);
          if (m3 && m3[1]) validTillTR = m3[1];
        }
      }
      if (/(NT)/i.test(l)) {
        const m = l.match(/([0-9\-]+)/);
        if (m && m[1]) validTillNT = m[1];
      }
      if (/(TR)/i.test(l)) {
        const m = l.match(/([0-9\-]+)/);
        if (m && m[1]) validTillTR = m[1];
      }
    }

    // DOB
    const dobLine = dlLines.find(l => /dob/i.test(l));
    if (dobLine) {
      const m = dobLine.match(/dob\s*[:：]?\s*([0-9\-]+)/i);
      if (m && m[1]) dob = m[1];
    } else {
      // fallback: first date after 'DOB'
      const dobIdx = dlLines.findIndex(l => /dob/i.test(l));
      if (dobIdx !== -1 && dlLines[dobIdx + 1]) {
        const m = dlLines[dobIdx + 1].match(/([0-9\-]+)/);
        if (m && m[1]) dob = m[1];
      }
    }

    // Name
    const nameLine = dlLines.find(l => /^name\b/i.test(l));
    if (nameLine) {
      const m = nameLine.match(/^name\s*([A-Z ]+)/i);
      if (m && m[1]) name = m[1].trim();
      else name = nameLine.replace(/^name/i, '').trim();
    }

    // Father Name
    const fatherLine = dlLines.find(l => /s\/d\/w of/i.test(l));
    if (fatherLine) {
      const m = fatherLine.match(/s\/d\/w of[:：]?\s*([A-Z ]+)/i);
      if (m && m[1]) fatherName = m[1].trim();
      else fatherName = fatherLine.replace(/.*s\/d\/w of[:：]?/i, '').trim();
    }

    // Blood Group
    const bgLine = dlLines.find(l => /bg\s*[:：]?/i.test(l));
    if (bgLine) {
      const m = bgLine.match(/bg\s*[:：]?\s*([A-Z+-]+)/i);
      if (m && m[1]) bloodGroup = m[1];
    }

    // Address: lines after 'Add' or 'Address'
    let addIdx = dlLines.findIndex(l => /^add\b|address/i.test(l));
    if (addIdx !== -1) {
      let addrLines = [];
      for (let i = addIdx + 1; i < dlLines.length; i++) {
        if (/pin|signature|impression|issuing authority|form|dl no|name|dob|doi|valid till|class|cov|father|mother|s\/d\/w/i.test(dlLines[i])) break;
        addrLines.push(dlLines[i].replace(/,$/, '').trim());
      }
      address = addrLines.join(', ');
      // Try to append PIN if found
      const pinLine = dlLines.find(l => /pin\s*\d{6}/i.test(l));
      if (pinLine) {
        const m = pinLine.match(/pin\s*(\d{6})/i);
        if (m && m[1]) address += (address ? ', ' : '') + 'PIN ' + m[1];
      }
    }

    // Classes of Vehicles
    for (let i = 0; i < dlLines.length; i++) {
      const covMatch = dlLines[i].match(/^(MCWG|LMV|TRANS)\s+([0-9\-]+)/i);
      if (covMatch) {
        classesOfVehicles.push({ COV: covMatch[1], DOI: covMatch[2] });
      }
    }

    // Issuing Authority
    const authLine = dlLines.find(l => /issuing authority/i.test(l));
    if (authLine) {
      const m = authLine.match(/issuing authority[:：]?\s*([A-Z0-9 ]+)/i);
      if (m && m[1]) issuingAuthority = m[1].trim();
      else {
        // fallback: last code-like string in line
        const m2 = authLine.match(/([A-Z]{2}\d{8,})/i);
        if (m2 && m2[1]) issuingAuthority = m2[1];
      }
    } else {
      // fallback: last code-like string in file
      for (let i = dlLines.length - 1; i >= 0; i--) {
        const m = dlLines[i].match(/([A-Z]{2}\d{8,})/i);
        if (m && m[1]) { issuingAuthority = m[1]; break; }
      }
    }

    // Build JSON
    return {
      Name: name,
      FatherName: fatherName,
      DateOfBirth: dob,
      BloodGroup: bloodGroup,
      Address: address,
      DL_Number: dlNumber,
      DateOfIssue: doi,
      ValidTill: {
        NonTransport: validTillNT,
        Transport: validTillTR
      },
      ClassesOfVehicles: classesOfVehicles,
      IssuingAuthority: issuingAuthority,
      documentType: 'driving_licence',
      filename: filename
    };
  }
  // Removed hardcoded MOA JSON. MOA extraction will use dynamic logic below.
  // Articles of Association (AOA) extraction
  // Heuristic: If document contains 'Articles of Association' or 'e-AOA', treat as AOA
  const isAOA = lines.some(l => /articles of association|e-aoa/i.test(l));
  if (isAOA) {
    // Company Name: look for 'The name of the company is' and next 1-2 lines
    let companyName = '';
    let nameIdx = lines.findIndex(l => /the name of the company is/i.test(l));
    if (nameIdx !== -1) {
      let nameLines = [];
      for (let i = nameIdx + 1; i < nameIdx + 3 && i < lines.length; i++) {
        if (lines[i].trim().length > 0) nameLines.push(lines[i].trim());
      }
      companyName = nameLines.join(' ');
    } else {
      // fallback: find first line with 'PRIVATE LIMITED'
      companyName = lines.find(l => /private limited/i.test(l)) || '';
    }

    // Company Type: look for 'One Person Company', 'Private Company', etc.
    let companyType = '';
    if (lines.some(l => /one person company/i.test(l))) {
      companyType = 'Private Limited - One Person Company';
    } else if (lines.some(l => /private company/i.test(l))) {
      companyType = 'Private Limited Company';
    }

    // Company Act: look for 'Companies Act, 2013'
    let companyAct = '';
    const actLine = lines.find(l => /companies act,? 2013/i.test(l));
    if (actLine) companyAct = 'Companies Act, 2013';

    // Share Capital: look for 'paid-up share capital' or similar
    let shareCapital = '';
    const capLine = lines.find(l => /paid[- ]?up share capital/i.test(l));
    if (capLine) shareCapital = capLine.replace(/.*paid[- ]?up share capital of/i, '').replace(/rupees/i, 'rupees').trim();
    if (!shareCapital) shareCapital = 'Minimum paid-up share capital of one lakh rupees';

    // Articles Applicable: look for 'Table F', 'Table G', etc.
    let articlesApplicable = '';
    const tableLine = lines.find(l => /table [fgh]/i.test(l));
    if (tableLine) {
      const m = tableLine.match(/table ([fgh])/i);
      if (m && m[1]) {
        if (m[1].toUpperCase() === 'F') articlesApplicable = 'Table F - A Company Limited by Shares';
        if (m[1].toUpperCase() === 'G') articlesApplicable = 'Table G - Company Limited by Guarantee and Having Share Capital';
        if (m[1].toUpperCase() === 'H') articlesApplicable = 'Table H - Company Limited by Guarantee and Not Having Share Capital';
      }
    }

    // Form No and Form Type
    let formNo = '';
    let formType = '';
    const formNoLine = lines.find(l => /form no\.? inc-34/i.test(l));
    if (formNoLine) formNo = 'INC-34';
    const formTypeLine = lines.find(l => /e-aoa/i.test(l));
    if (formTypeLine) formType = 'e-AOA (Articles of Association)';

    // Law Reference
    let lawReference = '';
    const lawLine = lines.find(l => /pursuant to section 5 of the companies act/i.test(l));
    if (lawLine) lawReference = 'Pursuant to Section 5 of the Companies Act, 2013 and rules made thereunder read with Schedule I';

    // Main Articles: extract article numbers, titles, and descriptions
    let mainArticles = [];
    // Map of article numbers to titles (based on your example)
    const articleMap = {
      1: 'Interpretation',
      2: 'Company Nature',
      3: 'Private Company',
      4: 'Share Capital and Variation of Rights',
      9: 'Lien',
      19: 'Transfer of Shares',
      23: 'Transmission of Shares',
      28: 'Forfeiture of Shares',
      35: 'Alteration of Capital',
      41: 'Buy-back of Shares',
      42: 'General Meetings',
      50: 'Voting Rights'
    };
    // For each article number, find its line and description
    for (const [num, title] of Object.entries(articleMap)) {
      const idx = lines.findIndex(l => new RegExp('^' + num + '\\b').test(l.trim()));
      if (idx !== -1) {
        // Description: join lines until next article number or empty line
        let descLines = [];
        for (let i = idx + 1; i < lines.length; i++) {
          if (/^\d+\b/.test(lines[i].trim()) || lines[i].trim() === '') break;
          descLines.push(lines[i].trim());
        }
        mainArticles.push({
          articleNo: parseInt(num),
          title,
          description: descLines.join(' ')
        });
      }
    }

    // Build the JSON structure
    return {
      company_details: {
        companyName,
        companyType,
        companyAct,
        shareCapital,
        articlesApplicable
      },
      documents: [
        {
          documentType: 'AOA',
          filename,
          details: {
            formNo,
            formType,
            lawReference,
            mainArticles
          }
        }
      ]
    };
  }
  // --- MEMORANDUM OF ASSOCIATION (MOA) EXTRACTION ---
  // Heuristic: If document contains 'Memorandum of Association' or 'e-MOA', treat as MOA
  const isMOA = lines.some(l => /memorandum of association|e-moa/i.test(l));
  if (isMOA) {
    // Company Name
    let companyName = '';
    let nameIdx = lines.findIndex(l => /the name of the company is/i.test(l));
    if (nameIdx !== -1) {
      let nameLine = lines[nameIdx + 1] ? lines[nameIdx + 1].trim() : '';
      companyName = nameLine;
    }
    if (!companyName) {
      companyName = lines.find(l => /\b(limited|ltd|llp|inc|corporation)\b/i.test(l) && l.split(' ').length >= 2) || '';
    }
    // State
    let state = '';
    let stateIdx = lines.findIndex(l => /registered office of the company will be situated in the state of/i.test(l));
    if (stateIdx !== -1) {
      state = lines[stateIdx + 1] ? lines[stateIdx + 1].replace(/\*/g, '').trim() : '';
      if (!state) state = lines[stateIdx].replace(/.*state of/i, '').replace(/\*/g, '').trim();
    }
    if (!state) {
      let m = lines.join(' ').match(/situated in the state of ([A-Za-z ]+)/i);
      if (m && m[1]) state = m[1].trim();
    }
    // Objects extraction
    let objects = [];
    let objStart = lines.findIndex(l => /objects to be pursued by the company/i.test(l));
    if (objStart !== -1) {
      let objBlock = [];
      for (let i = objStart + 1; i < lines.length; i++) {
        if (/liability of the member|the liability of the member|Page/i.test(lines[i])) break;
        objBlock.push(lines[i]);
      }
      let currentObj = '';
      for (let line of objBlock) {
        if (/^\d+\./.test(line)) {
          if (currentObj) objects.push(currentObj.trim());
          currentObj = line.replace(/^\d+\./, '').trim();
        } else {
          currentObj += (currentObj ? ' ' : '') + line.trim();
        }
      }
      if (currentObj) objects.push(currentObj.trim());
    }
    // Liability
    let liability = '';
    let liabIdx = lines.findIndex(l => /liability of the member\(s\) is limited/i.test(l));
    if (liabIdx !== -1) {
      liability = lines[liabIdx].trim();
    }
    // Share Capital
    let shareCapital = {
      TotalCapital: '',
      EquityShares: '',
      ValuePerShare: '',
      Currency: ''
    };
    let capIdx = lines.findIndex(l => /the share capital of the company/i.test(l));
    if (capIdx !== -1) {
      for (let i = capIdx; i < capIdx + 8 && i < lines.length; i++) {
        if (/rupees/i.test(lines[i]) && /\d{5,}/.test(lines[i])) shareCapital.TotalCapital = (lines[i].match(/(\d{5,})/)||[])[1] || '';
        if (/Equity|Preference/i.test(lines[i]) && /\d{1,6}/.test(lines[i])) shareCapital.EquityShares = (lines[i].match(/(\d{1,6})/)||[])[1] || '';
        if (/Shares of/i.test(lines[i]) && /\d{1,6}/.test(lines[i])) shareCapital.ValuePerShare = (lines[i].match(/(\d{1,6})/)||[])[1] || '';
        if (/rupees|rs\.?/i.test(lines[i])) shareCapital.Currency = (lines[i].match(/(rupees|rs\.?)/i)||[])[1] || '';
      }
    }
    // Subscriber Details
    let subscriberDetails = [];
    let subIdx = lines.findIndex(l => /Subscriber Details/i.test(l));
    if (subIdx !== -1) {
      let block = [];
      for (let i = subIdx + 1; i < lines.length; i++) {
        if (/Total shares taken|Signed before me|Witness|Membership type|Page/i.test(lines[i])) break;
        block.push(lines[i]);
      }
      let name = '', address = '', dinpan = '', sharesTaken = '', date = '', occupation = '';
      for (let i = 0; i < block.length; i++) {
        if (/Name:/i.test(block[i])) name = block[i].replace(/Name:/i, '').trim();
        if (/Address:/i.test(block[i])) address += (address ? ' ' : '') + block[i].replace(/Address:/i, '').trim();
        if (/\b[A-Z]{1}\*[A-Z]{1}\*[A-Z]{1}\*[0-9]{1}\*[0-9]{1}\*/.test(block[i])) dinpan = block[i].trim();
        if (/Equity|Preference/i.test(block[i])) sharesTaken = block[i].replace(/Preference/i, '').trim();
        if (/\d{2}\/\d{2}\/\d{4}/.test(block[i])) date = (block[i].match(/(\d{2}\/\d{2}\/\d{4})/)||[])[1] || '';
        if (/Occupation:/i.test(block[i])) occupation = block[i].replace(/Occupation:/i, '').trim();
        if (/\d{6}/.test(block[i]) || /Baner|Pune|Maharashtra|Flat|State Bank|Behind|Gaon|address/i.test(block[i])) address += (address ? ' ' : '') + block[i].trim();
      }
      if (name || address || dinpan || sharesTaken || date || occupation) {
        subscriberDetails.push({
          Name: name,
          Address: address.trim(),
          "DIN/PAN": dinpan,
          SharesTaken: sharesTaken,
          Date: date,
          Occupation: occupation
        });
      }
    }
    // Witness
    let witness = {};
    let witIdx = lines.findIndex(l => /Signed before me|Membership type|Witness/i.test(l));
    if (witIdx !== -1) {
      let block = [];
      for (let i = witIdx; i < witIdx + 8 && i < lines.length; i++) {
        if (/Nominee|shall be the nominee|Page/i.test(lines[i])) break;
        block.push(lines[i]);
      }
      let witName = '', witAddress = '', witMembership = '', witMembershipNumber = '', witDate = '', witOccupation = '';
      for (let i = 0; i < block.length; i++) {
        if (!witName) {
          let nameMatch = block[i].match(/^(Mr|Ms|Mrs)?\s*([A-Z][a-z]+(\s+[A-Z][a-z]+)+)/);
          if (nameMatch && nameMatch[2]) witName = nameMatch[2].trim();
        }
        if (!witMembership) {
          let memMatch = block[i].match(/(ACS|FCA|ACA|FCS|FCMA)/i);
          if (memMatch && memMatch[1]) witMembership = memMatch[1];
        }
        if (!witMembershipNumber) {
          let numMatch = block[i].match(/(\d{1,8}\*?)/);
          if (numMatch && numMatch[1]) witMembershipNumber = numMatch[1];
        }
        if (!witDate) {
          let dateMatch = block[i].match(/(\d{2}\/\d{2}\/\d{4})/);
          if (dateMatch && dateMatch[1]) witDate = dateMatch[1];
        }
        if (/Address:/i.test(block[i])) {
          witAddress += (witAddress ? ' ' : '') + block[i].replace(/Address:/i, '').trim();
        } else if (/\d{6}/.test(block[i]) || /Baner|Pune|Maharashtra|Flat|ALTUS|Laxman Nagar|Office|Opposite|Nandan|Probiz|Behind|Gaon|address/i.test(block[i])) {
          witAddress += (witAddress ? ' ' : '') + block[i].trim();
        }
        if (!witOccupation) {
          let occMatch = block[i].match(/Occupation:?[ ]*([A-Za-z ]+)/i);
          if (occMatch && occMatch[1]) witOccupation = occMatch[1].trim();
          else if (/secretary|director|manager|consultant|advocate|accountant/i.test(block[i])) witOccupation = block[i].trim();
        }
      }
      witness = {
        Name: witName,
        Address: witAddress.trim(),
        Membership: witMembership,
        MembershipNumber: witMembershipNumber,
        Date: witDate,
        Occupation: witOccupation
      };
    }
    // Nominee
    let nominee = {};
    let nomIdx = lines.findIndex(l => /shall be the nominee/i.test(l));
    if (nomIdx !== -1) {
      let block = [];
      for (let i = nomIdx; i < nomIdx + 8 && i < lines.length; i++) {
        if (/Page|Witness|Signed before me/i.test(lines[i])) break;
        block.push(lines[i]);
      }
      let nomName = '', nomRelation = '', nomAddress = '', nomAge = '', nomCountry = '';
      for (let i = 0; i < block.length; i++) {
        if (!nomName) {
          let nameMatch = block[i].match(/^(Shri|Smt|Ms|Mr|Mrs)?\s*([A-Z][a-z]+(\s+[A-Z][a-z]+)+)/);
          if (nameMatch && nameMatch[2]) nomName = nameMatch[2].trim();
        }
        if (!nomRelation) {
          let relMatch = block[i].match(/(Daughter|Son|Wife|Husband|Father|Mother) of ([A-Za-z ]+)/i);
          if (relMatch && relMatch[1] && relMatch[2]) nomRelation = relMatch[1] + ' of ' + relMatch[2].trim();
        }
        if (!nomAge) {
          let ageMatch = block[i].match(/(aged|age)\s*(\d{1,3})/i);
          if (ageMatch && ageMatch[2]) nomAge = ageMatch[2];
        }
        if (/resident of/i.test(block[i])) {
          nomAddress += (nomAddress ? ' ' : '') + block[i].replace(/resident of/i, '').trim();
        } else if (/\d{6}/.test(block[i]) || /Baner|Pune|Maharashtra|Flat|Behind|Gaon|address/i.test(block[i])) {
          nomAddress += (nomAddress ? ' ' : '') + block[i].trim();
        }
        if (!nomCountry && /India/i.test(block[i])) nomCountry = 'India';
      }
      nominee = {
        Name: nomName,
        Relation: nomRelation,
        Age: nomAge,
        Address: nomAddress.trim(),
        Country: nomCountry
      };
    }
    // Build JSON
    return {
      company_details: {
        ["company_name:" + companyName]: {
          documents: [
            {
              documentType: 'memorandum_of_association',
              filename: filename,
              CompanyName: companyName,
              State: state,
              Objects: objects,
              Liability: liability,
              ShareCapital: shareCapital,
              SubscriberDetails: subscriberDetails,
              Witness: witness,
              Nominee: nominee
            }
          ]
        }
      }
    };
  }
  // --- SOCIETY REGISTRATION CERTIFICATE (DYNAMIC) ---
  const societyPatterns = [
    /certificate of registration of societies/i,
    /societies registration act/i,
    /memorandum of association/i,
    /rules and regulations of/i
  ];
  const isSocietyReg = societyPatterns.some(pat => lines.some(l => pat.test(l)));
  if (isSocietyReg) {
    // Society Name
    let societyName = '';
    // Try various patterns
    let nameLine = lines.find(l => /name of the society/i.test(l));
    if (nameLine) {
      const m = nameLine.match(/name of the society.*?:?\s*"?([A-Za-z0-9 .,'&()\/-]+)"?/i);
      if (m && m[1]) societyName = m[1].trim();
    }
    if (!societyName) {
      // Try: 'has this day been registered' line
      const regLine = lines.find(l => /has this day been registered/i.test(l));
      if (regLine) {
        const m = regLine.match(/that (.+?) has this day been registered/i);
        if (m && m[1]) societyName = m[1].trim();
      }
    }
    if (!societyName) {
      // Try: all-caps line with 3+ words
      societyName = lines.find(l => l === l.toUpperCase() && l.split(' ').length >= 3 && /society/i.test(l)) || '';
    }
    if (!societyName) {
      // Try: first line with 'society' and 2+ words
      societyName = lines.find(l => /society/i.test(l) && l.split(' ').length >= 2) || '';
    }
    if (!societyName) societyName = 'Unknown';

    // Registration Number
    let registrationNumber = '';
    let regNumLine = lines.find(l => /s\.?no\.?|registration no\.?|regd\.? no\.?|regn\.? no\.?|no\./i.test(l));
    if (regNumLine) {
      const m = regNumLine.match(/(S\.?No\.?|Registration No\.?|Regd\.? No\.?|Regn\.? No\.?|No\.)\s*:?\s*([A-Za-z0-9\/-]+)/i);
      if (m && m[2]) registrationNumber = m[2].trim();
      else registrationNumber = regNumLine.replace(/.*(S\.?No\.?|Registration No\.?|Regd\.? No\.?|Regn\.? No\.?|No\.)/i, '').trim();
    }
    // Legacy Registration Number
    let legacyRegNum = '';
    let legacyLine = lines.find(l => /legacy registration no\.?/i.test(l));
    if (legacyLine) {
      const m = legacyLine.match(/legacy registration no\.?\s*:?\s*([A-Za-z0-9\/-]+)/i);
      if (m && m[1]) legacyRegNum = m[1].trim();
    }
    // Registration Date
    let registrationDate = '';
    let dateLine = lines.find(l => /this \d{1,2}(st|nd|rd|th)? day of|date :|date:/i.test(l));
    if (dateLine) {
      // Try to extract date
      let m = dateLine.match(/this (\d{1,2})(st|nd|rd|th)? day of ([A-Za-z]+)[, ]+([A-ZaZ ]+)?(\d{4})/i);
      if (m && m[1] && m[3] && m[4]) registrationDate = `${m[1]}-${m[3]}-${m[4]}`;
      else {
        m = dateLine.match(/date\s*:?\s*([0-9\/-]{6,})/i);
        if (m && m[1]) registrationDate = m[1].trim();
      }
    }
    if (!registrationDate) {
      // Try: any line with dd-mm-yyyy or dd/mm/yyyy
      let m = lines.join(' ').match(/(\d{1,2}[\/\-][A-Za-z]{3,9}[\/\-]\d{4})/);
      if (m && m[1]) registrationDate = m[1];
      else {
        m = lines.join(' ').match(/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})/);
        if (m && m[1]) registrationDate = m[1];
      }
    }
    // Registrar
    let registrar = '';
    let regLine = lines.find(l => /registrar of (firms|societies|companies)/i.test(l));
    if (regLine) {
      registrar = regLine.replace(/digitally signed by|signature valid|\(.*\)/i, '').trim();
      // Try to join with next line if it contains more info
      const idx = lines.indexOf(regLine);
      if (lines[idx + 1] && /company|societies|firms|non-trading|state|pondicherry|west bengal/i.test(lines[idx + 1])) {
        registrar += ', ' + lines[idx + 1].trim();
      }
    }
    // Location
    let location = '';
    let locLine = lines.find(l => /given under my hand at|place :|location|pondicherry|kolkata|delhi|mumbai|chennai|bangalore|hyderabad/i.test(l));
    if (locLine) {
      let m = locLine.match(/at ([A-Za-z ]+)/i);
      if (m && m[1]) location = m[1].trim();
      else location = locLine.replace(/place :|location/i, '').trim();
    }
    if (!location) {
      // Try to find city/state in lines
      location = lines.find(l => /(pondicherry|kolkata|delhi|mumbai|chennai|bangalore|hyderabad)/i.test(l)) || '';
    }
    // UniqueId
    let uniqueId = '';
    let uniqLine = lines.find(l => /unique number|unique id|unique no\.?/i.test(l));
    if (uniqLine) {
      const m = uniqLine.match(/unique (number|id|no\.?)[ :]*([A-Za-z0-9]+)/i);
      if (m && m[2]) uniqueId = m[2].trim();
    }
    // AdditionalDetails: join all lines that look like extra info
    let additionalDetails = '';
    let addLines = lines.filter(l => /certified copy|memorandum|rules and regulations|issued on|copy issued|true copy|area of operation|objectives|scope of functions|members of the governing body|witness|office at|office of the society|office location|address/i.test(l));
    if (addLines.length) additionalDetails = addLines.join(' | ');

    // Always fill missing values with ""
    const doc = {
      DocumentType: "society_registration",
      RegistrationNumber: registrationNumber || "",
      LegacyRegistrationNumber: legacyRegNum || "",
      RegistrationDate: registrationDate || "",
      SocietyName: societyName || "",
      Registrar: registrar || "",
      Location: location || "",
      UniqueId: uniqueId || "",
      AdditionalDetails: additionalDetails || ""
    };
    return {
      company_details: {
        ["company_name:" + (societyName || 'Unknown')]: {
          documents: [doc]
        }
      }
    };
  }
  // Debug: log lines and text
  console.log('--- Extracted lines for', filename, '---');
  lines.forEach(l => console.log(l));
  const text = lines.join(' ');

  // Regex patterns
  const panRegex = /([A-Z]{5}[0-9]{4}[A-Z])/i;
  const aadhaarRegex = /(\d{4}\s\d{4}\s\d{4})/;
  const gstRegex = /([0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1})/i;
  const dobRegex = /(\d{2}[\/\-]\d{2}[\/\-]\d{4})/;

  let pan = (text.match(panRegex) || [])[1];
  let aadhaar = (text.match(aadhaarRegex) || [])[1];
  let gst = (text.match(gstRegex) || [])[1];
  let dob = (text.match(dobRegex) || [])[1];
  let gender = '';
  if (/male/i.test(text)) gender = 'MALE';
  else if (/female/i.test(text)) gender = 'FEMALE';


  // GST certificate extraction
  if (gst) {
    let legalName = '';
    let tradeName = '';
    // Find Legal Name
    const legalIdx = lines.findIndex(l => /legal name/i.test(l));
    if (legalIdx !== -1 && lines[legalIdx + 1]) {
      legalName = lines[legalIdx + 1].trim();
    }
    // Find Trade Name
    const tradeIdx = lines.findIndex(l => /trade name/i.test(l));
    if (tradeIdx !== -1 && lines[tradeIdx + 1]) {
      tradeName = lines[tradeIdx + 1].trim();
    }
    return {
      filename,
      GST: gst,
      LegalName: legalName,
      TradeName: tradeName,
      documentType: 'gst'
    };
  }

  // Certificate of Incorporation extraction
  // Improved: If document contains 'Certificate of Incorporation', treat as Certificate of Incorporation
  // and try to extract CIN using a robust pattern
  const isCOICert = lines.some(l => /certificate of incorporation/i.test(l));
  let cinPattern = /([LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6})/i;
  let cinMatch = text.match(cinPattern);
  if (isCOICert && cinMatch) {
    const doc = { filename, documentType: 'certificate_of_incorporation' };
    // CompanyName: look for 'hereby certify that <NAME> is incorporated' or fallback to first all-caps line with 3+ words
    let companyName = '';
    const certLine = lines.find(l => /hereby certify that/i.test(l));
    if (certLine) {
      const m = certLine.match(/hereby certify that ([A-Z0-9 .,'()&-]+) is incorporated/i);
      if (m && m[1]) companyName = m[1].trim();
    }
    if (!companyName) {
      companyName = lines.find(l => l === l.toUpperCase() && l.split(' ').length >= 3) || '';
    }
    doc.CompanyName = companyName;
    // IncorporationDate: look for 'incorporated on this <date>' or 'day of <Month> <Year>'
    let incorporationDate = '';
    const incLine = lines.find(l => /incorporated on this/i.test(l));
    if (incLine) {
      // Try to extract date in 'Twenty third day of June Two thousand twenty-two'
      const dateMatch = incLine.match(/incorporated on this ([A-Za-z ]+ day of [A-ZaZ ]+ \d{4})/i);
      if (dateMatch && dateMatch[1]) {
        incorporationDate = dateMatch[1].replace(/\s+/g, ' ').trim();
      }
    }
    if (!incorporationDate) {
      const dayLine = lines.find(l => /day of/i.test(l) && /\d{4}/.test(l));
      if (dayLine) {
        const m = dayLine.match(/([A-Za-z ]+ day of [A-ZaZ ]+ \d{4})/i);
        if (m && m[1]) incorporationDate = m[1].replace(/\s+/g, ' ').trim();
      }
    }
    doc.IncorporationDate = incorporationDate;
    // CIN
    doc.CIN = cinMatch[1];
    // PAN
    let pan = '';
    const panLine = lines.find(l => /permanent account number/i.test(l));
    if (panLine) {
      const panMatch = panLine.match(/is ([A-Z0-9]+)/i);
      if (panMatch && panMatch[1]) pan = panMatch[1];
    }
    doc.PAN = pan;
    return doc;
  }

  // Cancelled Cheque extraction (strict, only for real cheques)
  const isCheque = lines.some(l => (/cheque|check|a\/c payee|payee|ifsc|cancelled/i.test(l) && /bank/i.test(lines.join(' '))) || (/pay to the order of|dollars|\$/i.test(l) && /bank|credit union|financial/i.test(lines.join(' '))));
  if (isCheque) {
    // Try to detect cheque type
    const isIndian = lines.some(l => /rupees|ifsc|a\/c|payee|rs\.|ltd|pvt|private|lacs|crore|authorised signatories/i.test(l));
    const isInternational = lines.some(l => /dollars|order of|pay to the|\$/i.test(l));

    // Common fields
    let bankName = lines.find(l => /bank|credit union|financial/i.test(l)) || '';
    let payeeName = '';
    let accountNumber = '';
    let ifsc = '';
    let chequeDate = '';
    let amountInWords = '';
    let amountInNumbers = '';
    let companyName = '';

    if (isIndian) {
      // ...existing Indian cheque extraction logic...
      // Bank Name
      bankName = lines.find(l => /bank/i.test(l)) || '';
      // Account Number
      accountNumber = lines.find(l => /^\d{6,}$/.test(l.trim())) || '';
      // IFSC
      for (const l of lines) {
        const m = l.match(/([A-Z]{4}0[A-Z0-9]{6})/i);
        if (m) { ifsc = m[1]; break; }
      }
      // Date
      const dateLine = lines.find(l => /date/i.test(l) && /\d{4}/.test(l));
      if (dateLine) {
        let d = dateLine.match(/(\d{4})\s*([A-Z]{3})\s*(\d{2})/i);
        if (d) chequeDate = `${d[1]}-${d[2]}-${d[3]}`;
        else {
          d = dateLine.match(/(\d{2})\s*([A-Z]{3})\s*(\d{4})/i);
          if (d) chequeDate = `${d[3]}-${d[2]}-${d[1]}`;
        }
      }
      // Payee Name
      const payIdx = lines.findIndex(l => /pay(ee)?/i.test(l));
      if (payIdx !== -1) {
        const payLine = lines[payIdx];
        const payMatch = payLine.match(/pay(?:ee)?\s*([A-Za-z0-9 .,&'-]+)/i);
        if (payMatch && payMatch[1] && payMatch[1].trim().length > 2) {
          payeeName = payMatch[1].trim();
        } else if (lines[payIdx + 1]) {
          payeeName = lines[payIdx + 1].trim();
        }
      }
      if (!payeeName) {
        payeeName = lines.find(l => /company|ltd|private|pvt|llp|inc|corporation/i.test(l)) || '';
      }
      // Amount in words
      let rupeesIdx = lines.findIndex(l => /^rupees\b/i.test(l) || /^\s*rupees\b/i.test(l));
      if (rupeesIdx === -1) {
        rupeesIdx = lines.findIndex(l => /\bRUPEES\b/i.test(l));
      }
      if (rupeesIdx !== -1) {
        let awLines = [];
        for (let i = rupeesIdx; i < lines.length; i++) {
          const line = lines[i];
          if (/^rs\.?/i.test(line) || line.trim() === '' || /\d{1,3}(,\d{2,3})*\.\d{2}/.test(line)) break;
          awLines.push(line);
        }
        if (awLines.length) {
          awLines[0] = awLines[0].replace(/^\u0000*RUPEES\s*/i, '');
          awLines[awLines.length-1] = awLines[awLines.length-1].replace(/\s*Only\s*$/i, '');
        }
        amountInWords = awLines.join(' ').replace(/\s+/g, ' ').trim();
        if (amountInWords) amountInWords += ' Rupees Only';
      }
      // Amount in numbers
      for (const l of lines) {
        const m = l.match(/([\d,]+\.\d{2})/);
        if (m) { amountInNumbers = m[1].replace(/,/g, ''); break; }
      }
      companyName = payeeName || bankName;
    } else if (isInternational) {
      // International cheque (e.g., Canadian)
      // Bank Name
      bankName = lines.find(l => /bank|credit union|financial/i.test(l)) || '';
      // Payee Name
      let payToIdx = lines.findIndex(l => /pay to the/i.test(l));
      if (payToIdx !== -1) {
        // Next non-empty line is payee
        for (let i = payToIdx + 1; i < lines.length; i++) {
          if (lines[i].trim() && !/order of|\$/i.test(lines[i])) {
            payeeName = lines[i].trim(); break;
          }
        }
      }
      // Amount in numbers
      let dollarLine = lines.find(l => /\$ ?[\d,.]+/i.test(l));
      if (dollarLine) {
        const m = dollarLine.match(/\$ ?([\d,.]+)/);
        if (m) amountInNumbers = m[1].replace(/,/g, '');
      } else {
        // fallback: any line with number and decimal
        for (const l of lines) {
          const m = l.match(/([\d,]+\.\d{2})/);
          if (m) { amountInNumbers = m[1].replace(/,/g, ''); break; }
        }
      }
      // Amount in words: look for line ending with 'DOLLARS' or containing 'DOLLARS'
      let wordsIdx = lines.findIndex(l => /dollars$/i.test(l.trim()));
      if (wordsIdx !== -1) {
        amountInWords = lines[wordsIdx].replace(/\s*DOLLARS\s*$/i, '').trim();
        // If previous line looks like part of amount in words, join it
        if (wordsIdx > 0 && /[a-z]/i.test(lines[wordsIdx-1])) {
          amountInWords = lines[wordsIdx-1].trim() + ' ' + amountInWords;
        }
        amountInWords += ' Dollars';
      }
      // Cheque Date: look for line with 'DATE' and 6+ digits (YYYYMMDD or similar)
      const dateLine = lines.find(l => /date/i.test(l) && /\d{6,}/.test(l));
      if (dateLine) {
        const m = dateLine.match(/(\d{8})/);
        if (m) chequeDate = m[1];
      }
      // Account Number: look for 6+ digit number at end of line
      accountNumber = lines.find(l => /\d{6,}/.test(l)) || '';
      companyName = payeeName || bankName;
    }

    return {
      filename,
      CompanyName: companyName || '',
      BankName: bankName || '',
      AccountNumber: accountNumber || '',
      IFSC: ifsc || '',
      Date: chequeDate || '',
      AmountWords: amountInWords || '',
      AmountNumbers: amountInNumbers || '',
      PayeeName: payeeName || '',
      documentType: 'cancelled_cheque'
    };
  }

  // Name extraction logic (simplified for demo)
  // Prevent PAN-only extraction for Certificate of Incorporation
  let name = '';
  let documentType = '';
  // Only extract PAN as personal if not a certificate_of_incorporation
  if (aadhaar) {
    documentType = 'aadhaar';
    // Aadhaar: pick first line after 'Government of India' with 2+ words, at least one word with both uppercase and lowercase letters
    const govIdx = lines.findIndex(l => /government of india/i.test(l));
    let foundName = '';
    for (let i = govIdx + 1; i < lines.length; i++) {
      const candidate = lines[i].trim();
      if (
        candidate.split(' ').length >= 2 &&
        candidate.split(' ').some(w => /[A-Z][a-z]+/.test(w)) &&
        !/download|date|dob|issue|male|female|vid|phh|\d{4}/i.test(candidate)
      ) {
        foundName = candidate;
        break;
      }
    }
    // Fallback: first line after 'Government of India' with 2+ words and at least one word with 3+ letters
    if (!foundName) {
      for (let i = govIdx + 1; i < lines.length; i++) {
        const candidate = lines[i].trim();
        if (
          candidate.split(' ').length >= 2 &&
          candidate.split(' ').some(w => w.length >= 3) &&
          !/download|date|dob|issue|male|female|vid|phh|\d{4}/i.test(candidate)
        ) {
          foundName = candidate;
          break;
        }
      }
    }
    name = foundName;
  } else if (pan) {
    // Only treat as PAN if not a certificate_of_incorporation
    if (!(lines.some(l => /certificate of incorporation/i.test(l)) && lines.some(l => /\bCIN\b/i.test(l)))) {
      documentType = 'pan';
      // PAN: look for line after 'Name'
      const nameIdx = lines.findIndex(l => /name/i.test(l));
      if (nameIdx !== -1 && lines[nameIdx + 1]) {
        const candidate = lines[nameIdx + 1].trim();
        if (candidate.split(' ').length >= 2 && /[a-zA-Z]{2,}/.test(candidate)) {
          name = candidate;
        }
      }
      // fallback: previous logic
      if (!name) {
        const panLineIdx = lines.findIndex(l => l.includes(pan));
        if (panLineIdx > 0) {
          const candidate = lines[panLineIdx - 1].trim();
          if (candidate.split(' ').length >= 2 && /[a-zA-Z]{2,}/.test(candidate)) {
            name = candidate;
          }
        }
      }
    }
  }
  // fallback name
  if (!name && !documentType) {
    name = lines.find(l => l.split(' ').length >= 2 && /[a-zA-Z]{2,}/.test(l)) || '';
  }

  // Build document object
  let doc = { filename };
  if (name) doc.Name = name;
  if (pan && documentType === 'pan') doc.PAN = pan;
  if (aadhaar) doc.Aadhaar = aadhaar;
  if (gst) doc.GST = gst;
  if (dob) doc.dob = dob;
  if (gender) doc.gender = gender;
  if (documentType) doc.documentType = documentType;

  return doc;
}

// POST /upload route for file uploads
app.post('/upload', upload.array('files'), async (req, res) => {
  try {
    const files = req.files;
    const results = [];
    for (const file of files) {
      // Upload to S3
      const fileContent = fs.readFileSync(file.path);
      const key = file.filename + path.extname(file.originalname);
      await s3.upload({
        Bucket: S3_BUCKET_NAME,
        Key: key,
        Body: fileContent,
        ContentType: file.mimetype
      }).promise();
      // Textract extraction
      const textractRes = await extractFromS3(key, file.mimetype);
      console.log('--- AWS TEXTRACT RAW RESULT FOR:', file.originalname, '---');
      console.dir(textractRes, { depth: null });
      // Print extracted lines for easier debugging
      let lines = [];
      if (textractRes && textractRes.Blocks) {
        lines = textractRes.Blocks.filter(b => b.BlockType === 'LINE' && b.Text).map(b => b.Text.trim());
      } else if (textractRes && textractRes.TextDetections) {
        lines = textractRes.TextDetections.filter(b => b.DetectedText).map(b => b.DetectedText.trim());
      }
      console.log('--- Extracted lines for', file.originalname, '---');
      lines.forEach(l => console.log(l));
      const mapped = parseTextractResponse(textractRes, file.originalname);
      results.push(mapped);
      fs.unlinkSync(file.path); // cleanup
    }
    const grouped = mapTextractData(results);
    res.json(grouped);
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: 'Extraction failed', details: err.message });
  }
});

app.listen(3001, () => {
  console.log('Server started on http://localhost:3001');
});
