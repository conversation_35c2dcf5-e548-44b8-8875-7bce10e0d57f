<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Textract Document Uploader</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <h1>Document Upload & Textract Extractor</h1>
    <form id="uploadForm">
      <input type="file" id="fileInput" name="files" multiple accept=".jpg,.jpeg,.png,.pdf" />
      <button type="submit">Upload & Extract</button>
    </form>
    <div id="result">
      <h2>Extracted JSON</h2>
      <pre id="jsonOutput"></pre>
    </div>
    <button id="api-doc-btn" style="margin: 16px 0; padding: 8px 16px; font-size: 1rem;">API Documentation</button>
  </div>
  <script src="script.js"></script>
  <script>
    document.getElementById('api-doc-btn').onclick = function() {
      window.open('api-doc.html', '_blank');
    };
  </script>
</body>
</html>
