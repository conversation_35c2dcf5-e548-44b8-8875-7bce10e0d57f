<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Textract API Documentation</title>
  <style>
    body { font-family: 'Segoe UI', <PERSON>l, sans-serif; background: #f9f9f9; color: #222; margin: 0; padding: 0; }
    .container { max-width: 900px; margin: 40px auto; background: #fff; padding: 32px 40px; border-radius: 10px; box-shadow: 0 2px 12px #0001; }
    h1, h2, h3 { color: #1a237e; }
    pre, code { background: #f4f4f4; color: #222; border-radius: 4px; padding: 2px 6px; }
    pre { padding: 12px; overflow-x: auto; }
    .section { margin-bottom: 32px; }
    .field-table { border-collapse: collapse; width: 100%; margin: 16px 0; }
    .field-table th, .field-table td { border: 1px solid #ddd; padding: 8px; }
    .field-table th { background: #e3e6f3; }
    .note { background: #fffbe7; border-left: 4px solid #ffe082; padding: 12px 18px; margin: 18px 0; border-radius: 4px; }
    @media (max-width: 600px) { .container { padding: 12px 4vw; } }
  </style>
</head>
<body>
  <div class="container">
    <h1>API Documentation: Textract Backend</h1>
    <div class="section">
      <h2>Overview</h2>
      <p>This API provides endpoints for uploading scanned document images or PDFs, extracting structured data using AWS Textract, and returning grouped personal and company details in JSON format.</p>
    </div>
    <div class="section">
      <h2>1. <code>POST /upload</code></h2>
      <p>Uploads one or more files (images or PDFs), processes them with AWS Textract, and returns extracted and grouped data.</p>
      <h3>Method</h3>
      <p><code>POST</code></p>
      <h3>Content-Type</h3>
      <p><code>multipart/form-data</code></p>
      <h3>Request Body</h3>
      <ul>
        <li><b>files</b>: One or more files to upload (field name: <code>files</code>). Accepts images (JPG, PNG, etc.) and PDFs.</li>
      </ul>
      <h4>Example (using curl):</h4>
      <pre><code>curl -X POST http://localhost:3001/upload \
  -F "files=@/path/to/file1.jpg" \
  -F "files=@/path/to/file2.pdf"</code></pre>
      <h4>Example (using Postman):</h4>
      <ul>
        <li>Set method to POST</li>
        <li>Set URL to <code>http://localhost:3001/upload</code></li>
        <li>In the Body tab, select <b>form-data</b></li>
        <li>Add key <code>files</code> (type: File) and select one or more files</li>
      </ul>
      <h3>Response</h3>
      <p>Returns a JSON object with two main keys:</p>
      <ul>
        <li><b>personal_details</b>: Array of grouped personal documents by name</li>
        <li><b>company_details</b>: Object of grouped company documents by company name</li>
      </ul>
      <h4>Example Response</h4>
      <pre><code>{
  "personal_details": [
    {
      "John Doe": {
        "documents": [
          { "Name": "John Doe", "PAN": "**********", ... }
        ]
      }
    }
  ],
  "company_details": {
    "company_name:Acme Corp": {
      "documents": [
        { "CompanyName": "Acme Corp", "GST": "22AAAAA0000A1Z5", ... }
      ]
    }
  }
}</code></pre>
      <h3>Field Descriptions</h3>
      <table class="field-table">
        <tr><th>Field</th><th>Description</th></tr>
        <tr><td><b>personal_details</b></td><td>Array of objects, each with a person's name as key and a <code>documents</code> array of extracted document data.</td></tr>
        <tr><td><b>company_details</b></td><td>Object with keys like <code>company_name:Acme Corp</code>, each containing a <code>documents</code> array of extracted company document data.</td></tr>
        <tr><td><b>documents</b></td><td>Each document object contains fields relevant to the document type (e.g., Name, PAN, Aadhaar, GST, CompanyName, etc.).</td></tr>
      </table>
      <h3>Error Response</h3>
      <p>HTTP 500 with JSON:</p>
      <pre><code>{
  "error": "Extraction failed",
  "details": "&lt;error message&gt;"
}</code></pre>
    </div>
    <div class="section">
      <h2>Notes</h2>
      <ul>
        <li>CORS is enabled for <code>http://localhost:3000</code>.</li>
        <li>Only POST <code>/upload</code> is exposed; all extraction and grouping is automatic.</li>
        <li>Document types supported: PAN, Aadhaar, Driving Licence, GST, Certificate of Incorporation, Partnership Deed, MOA, AOA, Cancelled Cheque, and more.</li>
        <li>Cheques and company documents are grouped under <b>company_details</b>.</li>
        <li>Personal documents are grouped under <b>personal_details</b> by detected name.</li>
      </ul>
    </div>
    <div class="section">
      <h2>Supported Document Types</h2>
      <ul>
        <li><b>PAN Card</b></li>
        <li><b>Aadhaar Card</b></li>
        <li><b>Driving Licence</b></li>
        <li><b>Passport</b></li>
        <li><b>Voter ID</b></li>
        <li><b>GST Certificate</b></li>
        <li><b>Certificate of Incorporation</b></li>
        <li><b>Partnership Deed</b></li>
        <li><b>MOA (Memorandum of Association)</b></li>
        <li><b>AOA (Articles of Association)</b></li>
        <li><b>Cancelled Cheque</b></li>
        <li><b>Society Registration Certificate</b></li>
        <li><b>Trust Deed</b></li>
        <li><b>Board Resolution</b></li>
        <li><b>Other Company Documents</b></li>
      </ul>
      <p>The following document types are supported. Each is grouped as either <b>Personal</b> or <b>Company</b> and lists the main keys extracted from each document:</p>
      <table class="field-table">
        <thead>
          <tr>
            <th>Document Type</th>
            <th>Group</th>
            <th>Keys Considered</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>PAN Card</td>
            <td>Personal</td>
            <td>Name, PAN, Father's Name, Date of Birth</td>
          </tr>
          <tr>
            <td>Aadhaar Card</td>
            <td>Personal</td>
            <td>Name, Aadhaar Number, Date of Birth, Gender, Address</td>
          </tr>
          <tr>
            <td>Driving Licence</td>
            <td>Personal</td>
            <td>Name, Licence Number, Date of Birth, Validity, Address</td>
          </tr>
          <tr>
            <td>Passport</td>
            <td>Personal</td>
            <td>Name, Passport Number, Date of Birth, Nationality, Address</td>
          </tr>
          <tr>
            <td>Voter ID</td>
            <td>Personal</td>
            <td>Name, Voter ID Number, Father's Name, Date of Birth, Address</td>
          </tr>
          <tr>
            <td>GST Certificate</td>
            <td>Company</td>
            <td>CompanyName, GST, Address, Constitution of Business, Date of Registration</td>
          </tr>
          <tr>
            <td>Certificate of Incorporation</td>
            <td>Company</td>
            <td>CompanyName, CIN, Date of Incorporation, Registered Address</td>
          </tr>
          <tr>
            <td>Partnership Deed</td>
            <td>Company</td>
            <td>Firm Name, Partners, Date of Partnership, Address</td>
          </tr>
          <tr>
            <td>MOA (Memorandum of Association)</td>
            <td>Company</td>
            <td>CompanyName, CIN, Subscribers, Registered Address</td>
          </tr>
          <tr>
            <td>AOA (Articles of Association)</td>
            <td>Company</td>
            <td>CompanyName, CIN, Subscribers, Registered Address</td>
          </tr>
          <tr>
            <td>Cancelled Cheque</td>
            <td>Company</td>
            <td>Account Holder Name, Account Number, IFSC, Bank Name, Branch</td>
          </tr>
          <tr>
            <td>Society Registration Certificate</td>
            <td>Company</td>
            <td>SocietyName, RegistrationNumber, RegistrationDate, Registrar, Location, UniqueId, AdditionalDetails</td>
          </tr>
          <tr>
            <td>Trust Deed</td>
            <td>Company</td>
            <td>TrustName, ExecutionDate, PlaceOfExecution, AuthorFounder, CorpusFund, RegisteredOffice, Jurisdiction, BoardOfTrustees, ObjectsOfTrust, ManagingTrustee, StampDutyPaid, DocumentNumber, BookReference</td>
          </tr>
          <tr>
            <td>Board Resolution</td>
            <td>Company</td>
            <td>CompanyName, ResolutionDate, MeetingAddress, AuthorizedPerson, ResolutionStatement, CertifiedBy, SealAndSignature</td>
          </tr>
          <tr>
            <td>Other Company Documents</td>
            <td>Company</td>
            <td>CompanyName, Document Type, Relevant Identifiers</td>
          </tr>
        </tbody>
      </table>
      <div class="note">
        <b>Note:</b> The actual keys extracted may vary depending on the document layout and quality. All company documents (including cheques, society registration certificates, trust deeds, and board resolutions) are grouped under <b>company_details</b>. Personal documents are grouped under <b>personal_details</b> by detected name.
      </div>
    </div>
    <div class="section">
      <h2>Contact</h2>
      <p>For support or questions, contact the API maintainer.</p>
    </div>
  </div>
</body>
</html>
