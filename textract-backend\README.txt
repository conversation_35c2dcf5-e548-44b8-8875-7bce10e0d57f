# Textract Backend

## Setup
1. Install dependencies:
   npm install

2. Start the server:
   npm start

- The server runs on http://localhost:3001
- POST /upload with form-data (key: files, multiple files)
- Returns grouped JSON as per your requirements.

## Notes
- AWS credentials and bucket are hardcoded for demo. For production, use environment variables.
- Textract parsing is mocked for demo. Adjust `parseTextractResponse` for real field extraction.
