// This script handles file upload, sends files to a backend (to be implemented), and renders the mapped JSON.
// For demo, it mocks the Textract response and mapping logic.

document.getElementById('uploadForm').addEventListener('submit', async function(e) {
  e.preventDefault();
  const files = document.getElementById('fileInput').files;
  if (!files.length) {
    alert('Please select at least one file.');
    return;
  }

  // Send files to backend
  const formData = new FormData();
  for (const file of files) {
    formData.append('files', file);
  }
  document.getElementById('jsonOutput').textContent = 'Extracting...';
  try {
    const res = await fetch('http://localhost:3001/upload', {
      method: 'POST',
      body: formData
    });
    if (!res.ok) throw new Error('Extraction failed');
    const data = await res.json();
    document.getElementById('jsonOutput').textContent = JSON.stringify(data, null, 2);
  } catch (err) {
    document.getElementById('jsonOutput').textContent = 'Error: ' + err.message;
  }
});
// No mapping or mock logic needed in frontend now
