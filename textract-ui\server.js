const express = require('express');
const path = require('path');
const app = express();

app.use(express.static(__dirname));

// Serve API documentation at /api-docs
app.get('/api-docs', (req, res) => {
  res.send(`
    <h1>Textract API Documentation</h1>
    <h2>POST /upload</h2>
    <p>Upload one or more PDF/image files for extraction and grouping.</p>
    <h3>Request</h3>
    <ul>
      <li>Method: <b>POST</b></li>
      <li>URL: <b>/upload</b></li>
      <li>Content-Type: <b>multipart/form-data</b></li>
      <li>Field name: <b>files</b> (multiple allowed)</li>
    </ul>
    <h3>Response</h3>
    <pre>{
  "personal_data": [
    {
      "Name": {
        "documents": [
          {
            "filename": "file.jpg",
            "Name": "Person Name",
            "Aadhaar": "xxxx xxxx xxxx",
            "PAN": "**********",
            "dob": "dd/mm/yyyy",
            "gender": "MALE|FEMALE",
            "documentType": "aadhaar|pan"
          }
        ]
      }
    }
  ],
  "company_details": [
    {
      "filename": "file.pdf",
      "GST": "22AAAAA0000A1Z5",
      "Company": "Company Name",
      "documentType": "gst"
    }
  ]
}</pre>
    <h3>Notes</h3>
    <ul>
      <li>Each document object includes a <b>documentType</b> field: "aadhaar", "pan", or "gst".</li>
      <li>Personal documents are grouped by normalized name.</li>
      <li>Company documents are grouped by normalized company name.</li>
    </ul>
  `);
});

app.listen(3000, () => {
  console.log('UI running at http://localhost:3000');
});
